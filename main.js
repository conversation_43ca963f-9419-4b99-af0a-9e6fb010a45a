// 前置知識
// 1. DOM (Document Object Model)
// 2. BOM (Browser Object Model)
// 3. API (Application Programming Interface)
// 4. const：只在區塊中有效，避免重新賦值影響全域功能
// 5. let：只在區塊中有效，可以重新賦值
// 6. var：全域有效，可以重新賦值
// 7. 區塊作用域：大括號內容

// 變數定義
const API_URL = "https://api.verbasync.com/v1";
console.log("API URL:", API_URL);
// 迴圈測試
for (let i = 0; i < 5; i++) {
  console.log("在迴圈內:", i);
}
// map test
const users = [
    { name: "<PERSON>", age: 30 },
    { name: "<PERSON>", age: 28 },
    { name: "<PERSON>", age: 32 },
];
const userNames = users.map(user => user.name);
console.log("Users:", userNames);
// 事件處理器
const handleTaskSubmit = (event) => {
    // 防止任務執行時刷新頁面
    event.preventDefault();
    alert("任務完成！");
};
// 事件監聽器
const taskForm = document.getElementById("task-form");
taskForm.addEventListener("submit", handleTaskSubmit);
// filter test
const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const evenNumbers = numbers.filter(number => number % 2 === 0);
console.log("Even Numbers:", evenNumbers);
