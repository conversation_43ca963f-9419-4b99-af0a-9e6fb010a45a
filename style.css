* {
    margin: 0;
    padding: 0;
    text-decoration: none;
    list-style: none;
    box-sizing: border-box;
    font-size: 16px;
    line-height: 1.6;
}

body {
    margin: 0;
    font-family: Arial, sans-serif;
    text-align: center;
}

main {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-color: #c2e3e8;
}

h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

section {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

button {
    padding: 0.5rem 1rem;
    font-size: 1rem;
    cursor: pointer;
    border: none;
    border-radius: 0.5rem;
    background-color: #007bff;
    color: white;
    transition: background-color 0.3s ease;
    margin-top: 1rem;
}
